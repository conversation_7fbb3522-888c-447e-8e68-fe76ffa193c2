import { supabase, TABLES } from '@/lib/supabase'

export interface AdminSettings {
  siteName: string
  siteDescription: string
  adminEmail: string
  allowRegistration: boolean
  requireEmailVerification: boolean
  autoApproveAds: boolean
  maxImagesPerAd: number
  adExpiryDays: number
  enableNotifications: boolean
  maintenanceMode: boolean
  enableAnalytics: boolean
  enableReferrals: boolean
  freeAdsLimit: number // New setting for free ads limit for new users
}

export class AdminSettingsService {
  /**
   * Get all admin settings with fallback to defaults
   */
  static async getSettings(): Promise<AdminSettings> {
    try {
      const { data, error } = await supabase
        .from('admin_settings')
        .select('key, value')

      if (error) {
        console.warn('Failed to fetch settings, using defaults:', error.message)
        return this.getDefaultSettings()
      }

      // Convert array of key-value pairs to object
      const settingsMap: Record<string, any> = {}
      data?.forEach(setting => {
        settingsMap[setting.key] = setting.value
      })

      // Return settings with proper types and defaults
      return {
        siteName: settingsMap.site_name || 'OKDOI',
        siteDescription: settingsMap.site_description || 'Premium Marketplace for Everything',
        adminEmail: settingsMap.admin_email || '<EMAIL>',
        allowRegistration: settingsMap.allow_registration ?? true,
        requireEmailVerification: settingsMap.require_email_verification ?? false,
        autoApproveAds: settingsMap.auto_approve_ads ?? false,
        maxImagesPerAd: settingsMap.max_images_per_ad ?? 10,
        adExpiryDays: settingsMap.ad_expiry_days ?? 30,
        enableNotifications: settingsMap.enable_notifications ?? true,
        maintenanceMode: settingsMap.maintenance_mode ?? false,
        enableAnalytics: settingsMap.enable_analytics ?? true,
        enableReferrals: settingsMap.enable_referrals ?? true,
        freeAdsLimit: settingsMap.free_ads_limit ?? 2
      }
    } catch (error) {
      console.warn('Error fetching settings, using defaults:', error)
      return this.getDefaultSettings()
    }
  }

  /**
   * Get default settings when table is not available
   */
  private static getDefaultSettings(): AdminSettings {
    return {
      siteName: 'OKDOI',
      siteDescription: 'Premium Marketplace for Everything',
      adminEmail: '<EMAIL>',
      allowRegistration: true,
      requireEmailVerification: false,
      autoApproveAds: false,
      maxImagesPerAd: 10,
      adExpiryDays: 30,
      enableNotifications: true,
      maintenanceMode: false,
      enableAnalytics: true,
      enableReferrals: true,
      freeAdsLimit: 2
    }
  }

  /**
   * Update admin settings
   */
  static async updateSettings(settings: Partial<AdminSettings>): Promise<void> {
    const updates = []

    // Convert settings object to database format
    if (settings.siteName !== undefined) {
      updates.push({ key: 'site_name', value: settings.siteName })
    }
    if (settings.siteDescription !== undefined) {
      updates.push({ key: 'site_description', value: settings.siteDescription })
    }
    if (settings.adminEmail !== undefined) {
      updates.push({ key: 'admin_email', value: settings.adminEmail })
    }
    if (settings.allowRegistration !== undefined) {
      updates.push({ key: 'allow_registration', value: settings.allowRegistration })
    }
    if (settings.requireEmailVerification !== undefined) {
      updates.push({ key: 'require_email_verification', value: settings.requireEmailVerification })
    }
    if (settings.autoApproveAds !== undefined) {
      updates.push({ key: 'auto_approve_ads', value: settings.autoApproveAds })
    }
    if (settings.maxImagesPerAd !== undefined) {
      updates.push({ key: 'max_images_per_ad', value: settings.maxImagesPerAd })
    }
    if (settings.adExpiryDays !== undefined) {
      updates.push({ key: 'ad_expiry_days', value: settings.adExpiryDays })
    }
    if (settings.enableNotifications !== undefined) {
      updates.push({ key: 'enable_notifications', value: settings.enableNotifications })
    }
    if (settings.maintenanceMode !== undefined) {
      updates.push({ key: 'maintenance_mode', value: settings.maintenanceMode })
    }
    if (settings.enableAnalytics !== undefined) {
      updates.push({ key: 'enable_analytics', value: settings.enableAnalytics })
    }
    if (settings.enableReferrals !== undefined) {
      updates.push({ key: 'enable_referrals', value: settings.enableReferrals })
    }
    if (settings.freeAdsLimit !== undefined) {
      updates.push({ key: 'free_ads_limit', value: settings.freeAdsLimit })
    }

    // Update each setting with proper conflict resolution
    for (const update of updates) {
      const { error } = await supabase
        .from('admin_settings')
        .upsert({
          key: update.key,
          value: update.value,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'key',
          ignoreDuplicates: false
        })

      if (error) {
        throw new Error(`Failed to update setting ${update.key}: ${error.message}`)
      }
    }
  }

  /**
   * Get a specific setting value with fallback defaults
   */
  static async getSetting(key: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('admin_settings')
        .select('value')
        .eq('key', key)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return this.getDefaultSetting(key) // Setting not found, return default
        }
        // If table doesn't exist or other error, return default
        console.warn(`Error fetching setting ${key}:`, error.message)
        return this.getDefaultSetting(key)
      }

      return data?.value
    } catch (error) {
      console.warn(`Error fetching setting ${key}:`, error)
      return this.getDefaultSetting(key)
    }
  }

  /**
   * Get default value for a setting
   */
  private static getDefaultSetting(key: string): any {
    const defaults: Record<string, any> = {
      'site_name': 'OKDOI',
      'site_description': 'Premium Marketplace for Everything',
      'admin_email': '<EMAIL>',
      'allow_registration': true,
      'require_email_verification': false,
      'auto_approve_ads': false,
      'max_images_per_ad': 10,
      'ad_expiry_days': 30,
      'enable_notifications': true,
      'maintenance_mode': false,
      'enable_analytics': true,
      'enable_referrals': true,
      'free_ads_limit': 2
    }

    return defaults[key] || null
  }

  /**
   * Update a specific setting
   */
  static async updateSetting(key: string, value: any): Promise<void> {
    const { error } = await supabase
      .from('admin_settings')
      .upsert({
        key,
        value,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'key',
        ignoreDuplicates: false
      })

    if (error) {
      throw new Error(`Failed to update setting ${key}: ${error.message}`)
    }
  }
}
