"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    console.warn(\"Invalid referral code provided, continuing without referrer\");\n                // Don't fail signup for invalid referral code\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // If user is created and email verification is NOT required, create profile immediately\n        if (data.user && !requireEmailVerification) {\n            try {\n                await this.createUserProfile(data.user, userData, referrer);\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile in public.users table\n   */ static async createUserProfile(authUser, userData, referrer) {\n        try {\n            // Check if admin client is available\n            if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                console.error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n                throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n            }\n            // Use service role client to bypass RLS for user creation\n            const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n                id: authUser.id,\n                email: authUser.email,\n                full_name: (userData === null || userData === void 0 ? void 0 : userData.full_name) || null,\n                phone: (userData === null || userData === void 0 ? void 0 : userData.phone) || null,\n                location: (userData === null || userData === void 0 ? void 0 : userData.location) || null,\n                user_type: \"user\",\n                referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null,\n                is_verified: false,\n                role: \"user\"\n            });\n            if (profileError) {\n                console.error(\"Profile creation error:\", profileError);\n                throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n            }\n            console.log(\"User profile created successfully for:\", authUser.email);\n            // If user has a referrer, place them in hierarchy\n            if (referrer) {\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                    console.log(\"User placed in referral hierarchy successfully\");\n                } catch (referralError) {\n                    console.error(\"Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in createUserProfile:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});