{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "nhYMMbWc8Fnz5mvZRwT1X1Sn0CFSZ4nfCQ2DmmhOTXM="}}}, "functions": {}, "sortedMiddleware": ["/"]}