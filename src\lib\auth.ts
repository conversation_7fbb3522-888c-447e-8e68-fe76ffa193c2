import { supabase, supabaseAdmin } from './supabase'
import { User } from '@/types'
import { AdminSettingsService } from './services/adminSettings'
import { ReferralSystemService } from './services/referralSystem'

export interface AuthUser {
  id: string
  email: string
  full_name?: string
  phone?: string
  location?: string
  avatar_url?: string
}

export class AuthService {
  /**
   * Sign up a new user with OTP verification and referral processing
   */
  static async signUp(email: string, password: string, userData?: Partial<User> & { referralCode?: string }) {
    // Check if email verification is required (with fallback to false if table doesn't exist)
    let requireEmailVerification = false
    try {
      requireEmailVerification = await AdminSettingsService.getSetting('require_email_verification') || false
    } catch (error) {
      console.warn('Could not fetch email verification setting, defaulting to false:', error)
      requireEmailVerification = false
    }

    // Validate referral code if provided
    let referrer: User | null = null
    if (userData?.referralCode) {
      try {
        referrer = await ReferralSystemService.validateReferralCode(userData.referralCode)
        if (!referrer) {
          throw new Error('Invalid referral code')
        }
      } catch (error) {
        console.warn('Could not validate referral code:', error)
        // Don't fail signup for referral code issues, just log the warning
      }
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          ...userData,
          referral_code: userData?.referralCode, // Store referral code in auth metadata
          full_name: userData?.full_name,
          phone: userData?.phone,
          location: userData?.location,
          referrer_id: referrer?.id
        },
        // Don't set emailRedirectTo for OTP flow
      }
    })

    if (error) {
      throw new Error(error.message)
    }

    // If user is created and email verification is NOT required, create profile immediately
    if (data.user && !requireEmailVerification) {
      try {
        await this.createUserProfile(data.user, userData, referrer)
      } catch (profileError) {
        console.error('Error creating user profile:', profileError)
        // Don't fail the signup, but log the error
      }
    }

    return { data, requireEmailVerification, referrer }
  }

  /**
   * Create user profile in public.users table
   */
  private static async createUserProfile(authUser: any, userData?: Partial<User> & { referralCode?: string }, referrer?: User | null) {
    // Check if admin client is available
    if (!supabaseAdmin) {
      throw new Error('Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY')
    }

    // Use service role client to bypass RLS for user creation
    const { error: profileError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authUser.id,
        email: authUser.email!,
        full_name: userData?.full_name,
        phone: userData?.phone,
        location: userData?.location,
        user_type: 'user', // Default user type
        referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,
        referred_by_id: referrer?.id || null
      })

    if (profileError) {
      throw new Error(`Failed to create user profile: ${profileError.message}`)
    }

    // If user has a referrer, place them in hierarchy
    if (referrer) {
      try {
        await ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id)
      } catch (referralError) {
        console.error('Failed to place user in referral hierarchy:', referralError)
        // Don't fail the profile creation, but log the error
      }
    }
  }

  /**
   * Verify email OTP and process referral placement
   */
  static async verifyEmailOtp(email: string, token: string) {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'signup'
    })

    if (error) {
      throw new Error(error.message)
    }

    // After successful verification, create user profile if it doesn't exist
    if (data.user) {
      try {
        // Check if user profile already exists
        const { data: existingUser } = await supabase
          .from('users')
          .select('id')
          .eq('id', data.user.id)
          .single()

        if (!existingUser) {
          // Get referrer if referral code exists
          let referrer: User | null = null
          if (data.user.user_metadata?.referral_code) {
            referrer = await ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code)
          }

          // Create user profile
          await this.createUserProfile(data.user, {
            full_name: data.user.user_metadata?.full_name,
            phone: data.user.user_metadata?.phone,
            location: data.user.user_metadata?.location,
            referralCode: data.user.user_metadata?.referral_code
          }, referrer)
        }
      } catch (profileError) {
        console.error('Error creating user profile after verification:', profileError)
        // Don't fail the verification, but log the error
      }
    }

    return data
  }

  /**
   * Resend email OTP
   */
  static async resendEmailOtp(email: string) {
    const { data, error } = await supabase.auth.resend({
      type: 'signup',
      email
    })

    if (error) {
      throw new Error(error.message)
    }

    return data
  }

  /**
   * Sign in with email and password
   */
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      throw new Error(error.message)
    }

    return data
  }

  /**
   * Sign out the current user
   */
  static async signOut() {
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Get the current user session
   */
  static async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      throw new Error(error.message)
    }

    return session
  }

  /**
   * Get the current user with improved error handling
   */
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      // First check if we have a valid session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        console.error('Session error:', sessionError)
        return null
      }

      if (!session?.user) {
        console.log('No active session found')
        return null
      }

      const user = session.user
      console.log('Found active session for user:', user.email)

      // Get additional user data from users table with retry logic
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries) {
        try {
          const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single()

          if (profileError) {
            if (profileError.code === 'PGRST116') {
              // User profile doesn't exist, create it
              console.log('User profile not found, creating...')
              const newProfile = {
                id: user.id,
                email: user.email!,
                full_name: user.user_metadata?.full_name || null,
                phone: user.user_metadata?.phone || null,
                location: user.user_metadata?.location || null,
                role: 'user',
                is_super_admin: false
              }

              const { data: createdProfile, error: createError } = await supabase
                .from('users')
                .insert(newProfile)
                .select()
                .single()

              if (createError) {
                console.error('Error creating user profile:', createError)
                return {
                  id: user.id,
                  email: user.email!
                }
              }

              return createdProfile
            } else {
              throw profileError
            }
          }

          return profile
        } catch (error) {
          retryCount++
          console.error(`Error fetching user profile (attempt ${retryCount}):`, error)

          if (retryCount >= maxRetries) {
            console.error('Max retries reached, returning basic user info')
            return {
              id: user.id,
              email: user.email!
            }
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
        }
      }

      return null
    } catch (error) {
      console.error('Error in getCurrentUser:', error)
      return null
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, updates: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  }

  /**
   * Reset password
   */
  static async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Update password
   */
  static async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Listen to auth state changes with improved handling
   */
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change detected:', event, session?.user?.email)

      // Add a small delay to ensure state consistency
      setTimeout(() => {
        callback(event, session)
      }, 100)
    })
  }

  /**
   * Check if current session is valid
   */
  static async isSessionValid(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error || !session) {
        return false
      }

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000)
      if (session.expires_at && session.expires_at < now) {
        console.log('Session token expired')
        return false
      }

      return true
    } catch (error) {
      console.error('Error checking session validity:', error)
      return false
    }
  }

  /**
   * Refresh session if needed
   */
  static async refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        console.error('Error refreshing session:', error)
        return false
      }

      return !!data.session
    } catch (error) {
      console.error('Error in refreshSession:', error)
      return false
    }
  }
}
