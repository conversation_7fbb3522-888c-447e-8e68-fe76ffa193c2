"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/post-ad/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // If user is created and email verification is NOT required, create profile immediately\n        if (data.user && !requireEmailVerification) {\n            try {\n                await this.createUserProfile(data.user, userData, referrer);\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile in public.users table\n   */ static async createUserProfile(authUser, userData, referrer) {\n        // Check if admin client is available\n        if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n        }\n        // Use service role client to bypass RLS for user creation\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n            referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n        });\n        if (profileError) {\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n        // If user has a referrer, place them in hierarchy\n        if (referrer) {\n            try {\n                await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n            } catch (referralError) {\n                console.error(\"Failed to place user in referral hierarchy:\", referralError);\n            // Don't fail the profile creation, but log the error\n            }\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});