-- Comprehensive Database Fix Script for OKDOI
-- This script fixes admin settings, user signup, and KYC system issues

-- =====================================================
-- 1. FIX ADMIN SETTINGS TABLE
-- =====================================================

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster key lookups
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);

-- Insert default settings (with proper conflict handling)
INSERT INTO admin_settings (key, value, description, created_at, updated_at) VALUES
('site_name', '"OKDOI"', 'Name of the website', NOW(), NOW()),
('site_description', '"Premium Marketplace for Everything"', 'Description of the website', NOW(), NOW()),
('admin_email', '"<EMAIL>"', 'Administrator email address', NOW(), NOW()),
('allow_registration', 'true', 'Whether new user registration is allowed', NOW(), NOW()),
('require_email_verification', 'false', 'Whether email verification is required for new users', NOW(), NOW()),
('auto_approve_ads', 'false', 'Whether ads are automatically approved', NOW(), NOW()),
('max_images_per_ad', '10', 'Maximum number of images per ad', NOW(), NOW()),
('ad_expiry_days', '30', 'Number of days before ads expire', NOW(), NOW()),
('enable_notifications', 'true', 'Whether notifications are enabled', NOW(), NOW()),
('maintenance_mode', 'false', 'Whether the site is in maintenance mode', NOW(), NOW()),
('enable_analytics', 'true', 'Whether analytics are enabled', NOW(), NOW()),
('enable_referrals', 'true', 'Whether referral system is enabled', NOW(), NOW()),
('free_ads_limit', '2', 'Number of free ads new users can post without a subscription', NOW(), NOW())
ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = NOW();

-- Enable RLS
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admin users can read admin settings" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can insert admin settings" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can update admin settings" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can delete admin settings" ON admin_settings;

-- Create RLS policies
CREATE POLICY "Admin users can read admin settings" ON admin_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "Admin users can insert admin settings" ON admin_settings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "Admin users can update admin settings" ON admin_settings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "Admin users can delete admin settings" ON admin_settings
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_admin_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_admin_settings_updated_at_trigger ON admin_settings;
CREATE TRIGGER update_admin_settings_updated_at_trigger
    BEFORE UPDATE ON admin_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_admin_settings_updated_at();

-- =====================================================
-- 2. FIX USER SIGNUP DEPENDENCIES
-- =====================================================

-- Ensure users table has all required columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user';
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_super_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_until TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_referrals INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_rewards DECIMAL(10,2) DEFAULT 0;

-- Add referral system columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS user_type VARCHAR(20) DEFAULT 'user';
ALTER TABLE users ADD COLUMN IF NOT EXISTS referred_by_id UUID REFERENCES users(id);
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_level INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_path TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS direct_referrals_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_downline_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_commission_earned DECIMAL(10,2) DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_referral_active BOOLEAN DEFAULT true;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_super_admin ON users(is_super_admin);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
CREATE INDEX IF NOT EXISTS idx_users_referred_by_id ON users(referred_by_id);

-- Add constraint to ensure role is valid
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'check_user_role' 
        AND table_name = 'users'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT check_user_role 
            CHECK (role IN ('user', 'admin', 'moderator'));
    END IF;
END $$;

-- Add constraint to ensure user_type is valid
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'check_user_type'
        AND table_name = 'users'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT check_user_type
            CHECK (user_type IN ('okdoi_head', 'zonal_manager', 'rsm', 'user'));
    END IF;
END $$;

-- =====================================================
-- 3. FIX KYC SYSTEM SCHEMA
-- =====================================================

-- Add KYC status to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted';
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;

-- Add constraint for KYC status
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'check_kyc_status'
        AND table_name = 'users'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT check_kyc_status
            CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));
    END IF;
END $$;

-- Create KYC submissions table
CREATE TABLE IF NOT EXISTS kyc_submissions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

    -- Document URLs (stored in Supabase Storage)
    id_document_front_url text NOT NULL,
    id_document_back_url text NOT NULL,
    selfie_photo_url text NOT NULL,
    address_proof_url text NOT NULL,

    -- Document metadata
    id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
    id_document_number varchar(100),

    -- Personal information for verification
    full_name varchar(255) NOT NULL,
    date_of_birth date,
    address text NOT NULL,

    -- Submission status and tracking
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
    submission_notes text,

    -- Admin review fields
    reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    reviewed_at timestamp with time zone,
    rejection_reason text,
    admin_notes text,

    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),

    -- Ensure one active submission per user
    UNIQUE(user_id)
);

-- Create KYC status history table for audit trail
CREATE TABLE IF NOT EXISTS kyc_status_history (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    kyc_submission_id uuid REFERENCES kyc_submissions(id) ON DELETE CASCADE NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

    -- Status change tracking
    previous_status varchar(20),
    new_status varchar(20) NOT NULL,
    changed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    change_reason text,
    admin_notes text,

    -- Timestamps
    created_at timestamp with time zone DEFAULT now()
);

-- Create KYC document types table
CREATE TABLE IF NOT EXISTS kyc_document_types (
    id varchar(50) PRIMARY KEY,
    name varchar(100) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now()
);

-- Insert default document types
INSERT INTO kyc_document_types (id, name, description) VALUES
('national_id', 'National Identity Card', 'Government issued national identity card'),
('passport', 'Passport', 'International passport document'),
('driving_license', 'Driving License', 'Government issued driving license')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description;

-- Create indexes for KYC tables
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_user_id ON kyc_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_status ON kyc_submissions(status);
CREATE INDEX IF NOT EXISTS idx_kyc_status_history_user_id ON kyc_status_history(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_status_history_submission_id ON kyc_status_history(kyc_submission_id);

-- Enable RLS for KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;

-- KYC RLS Policies
-- Users can only see their own submissions
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (user_id = auth.uid());

-- Users can insert their own submissions
CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can update their own pending submissions
CREATE POLICY "Users can update own pending KYC submissions" ON kyc_submissions
    FOR UPDATE USING (user_id = auth.uid() AND status = 'pending');

-- Admins can view all submissions
CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Admins can update all submissions
CREATE POLICY "Admins can update all KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- KYC status history policies
CREATE POLICY "Users can view own KYC history" ON kyc_status_history
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all KYC history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "System can insert KYC history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

-- Document types - everyone can read
CREATE POLICY "Everyone can read document types" ON kyc_document_types
    FOR SELECT USING (true);

-- Create KYC helper function
CREATE OR REPLACE FUNCTION is_kyc_verified(user_uuid uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users
        WHERE id = user_uuid
        AND kyc_status = 'approved'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update KYC status
CREATE OR REPLACE FUNCTION update_user_kyc_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user's KYC status based on submission status
    IF NEW.status = 'approved' THEN
        UPDATE users SET
            kyc_status = 'approved',
            kyc_approved_at = NOW()
        WHERE id = NEW.user_id;
    ELSIF NEW.status = 'rejected' THEN
        UPDATE users SET
            kyc_status = 'rejected',
            kyc_approved_at = NULL
        WHERE id = NEW.user_id;
    ELSIF NEW.status = 'pending' OR NEW.status = 'under_review' THEN
        UPDATE users SET
            kyc_status = NEW.status,
            kyc_submitted_at = COALESCE(users.kyc_submitted_at, NOW()),
            kyc_approved_at = NULL
        WHERE id = NEW.user_id;
    END IF;

    -- Insert into status history
    INSERT INTO kyc_status_history (
        kyc_submission_id,
        user_id,
        previous_status,
        new_status,
        changed_by,
        change_reason,
        admin_notes
    ) VALUES (
        NEW.id,
        NEW.user_id,
        OLD.status,
        NEW.status,
        NEW.reviewed_by,
        NEW.rejection_reason,
        NEW.admin_notes
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for KYC status updates
DROP TRIGGER IF EXISTS update_user_kyc_status_trigger ON kyc_submissions;
CREATE TRIGGER update_user_kyc_status_trigger
    AFTER UPDATE ON kyc_submissions
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
    EXECUTE FUNCTION update_user_kyc_status();

-- =====================================================
-- 4. CREATE MISSING FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to generate referral codes
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS TEXT AS $$
DECLARE
    code TEXT;
    exists_check INTEGER;
BEGIN
    LOOP
        -- Generate a random 8-character code
        code := upper(substring(md5(random()::text) from 1 for 8));

        -- Check if code already exists
        SELECT COUNT(*) INTO exists_check FROM users WHERE referral_code = code;

        -- If code doesn't exist, return it
        IF exists_check = 0 THEN
            RETURN code;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to assign referral code to new users
CREATE OR REPLACE FUNCTION assign_referral_code()
RETURNS TRIGGER AS $$
BEGIN
    -- Only assign if referral_code is null
    IF NEW.referral_code IS NULL THEN
        NEW.referral_code := generate_referral_code();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to assign referral codes
DROP TRIGGER IF EXISTS assign_referral_code_trigger ON users;
CREATE TRIGGER assign_referral_code_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION assign_referral_code();

-- Update existing users without referral codes
UPDATE users SET referral_code = generate_referral_code() WHERE referral_code IS NULL;

-- =====================================================
-- 5. ENSURE PROPER RLS POLICIES FOR USERS TABLE
-- =====================================================

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON users;

-- Create proper user policies
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Allow viewing basic user info for public profiles (for referrals, etc.)
CREATE POLICY "Public user info viewable" ON users
    FOR SELECT USING (true);

-- Admin policies
CREATE POLICY "Admins can view all users" ON users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id = auth.uid()
            AND (u.role = 'admin' OR u.is_super_admin = true)
        )
    );

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- Add a comment to indicate completion
COMMENT ON TABLE admin_settings IS 'Admin settings table - Fixed upsert issues and RLS policies';
COMMENT ON TABLE kyc_submissions IS 'KYC submissions table - Complete implementation with proper constraints';
COMMENT ON TABLE users IS 'Users table - Enhanced with referral system and KYC status columns';
