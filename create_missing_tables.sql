-- Create missing tables for OKDOI marketplace
-- Run this in Supabase SQL Editor

-- 1. Create admin_settings table
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster key lookups
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);

-- Insert default settings
INSERT INTO admin_settings (key, value, description, created_at, updated_at) VALUES
('site_name', '"OKDOI"', 'Name of the website', NOW(), NOW()),
('site_description', '"Premium Marketplace for Everything"', 'Description of the website', NOW(), NOW()),
('admin_email', '"<EMAIL>"', 'Administrator email address', NOW(), NOW()),
('allow_registration', 'true', 'Whether new user registration is allowed', NOW(), NOW()),
('require_email_verification', 'false', 'Whether email verification is required for new users', NOW(), NOW()),
('auto_approve_ads', 'false', 'Whether ads are automatically approved', NOW(), NOW()),
('max_images_per_ad', '10', 'Maximum number of images per ad', NOW(), NOW()),
('ad_expiry_days', '30', 'Number of days before ads expire', NOW(), NOW()),
('enable_notifications', 'true', 'Whether notifications are enabled', NOW(), NOW()),
('maintenance_mode', 'false', 'Whether the site is in maintenance mode', NOW(), NOW()),
('enable_analytics', 'true', 'Whether analytics are enabled', NOW(), NOW()),
('enable_referrals', 'true', 'Whether referral system is enabled', NOW(), NOW()),
('free_ads_limit', '2', 'Number of free ads new users can post without a subscription', NOW(), NOW())
ON CONFLICT (key) DO NOTHING;

-- 2. Add KYC columns to users table if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted' 
CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));

ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;

-- 3. Create KYC submissions table
CREATE TABLE IF NOT EXISTS kyc_submissions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Document URLs (stored in Supabase Storage)
    id_document_front_url text NOT NULL,
    id_document_back_url text NOT NULL,
    selfie_photo_url text NOT NULL,
    address_proof_url text NOT NULL,
    
    -- Document metadata
    id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
    id_document_number varchar(100),
    
    -- Personal information for verification
    full_name varchar(255) NOT NULL,
    date_of_birth date,
    address text NOT NULL,
    
    -- Submission status and tracking
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
    submission_notes text,
    
    -- Admin review fields
    reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    reviewed_at timestamp with time zone,
    rejection_reason text,
    admin_notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Ensure one active submission per user
    UNIQUE(user_id)
);

-- 4. Create KYC status history table
CREATE TABLE IF NOT EXISTS kyc_status_history (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    kyc_submission_id uuid REFERENCES kyc_submissions(id) ON DELETE CASCADE NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    previous_status varchar(20),
    new_status varchar(20) NOT NULL,
    changed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    change_reason text,
    admin_notes text,
    created_at timestamp with time zone DEFAULT now()
);

-- 5. Create KYC document types reference table
CREATE TABLE IF NOT EXISTS kyc_document_types (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    type_code varchar(50) NOT NULL UNIQUE,
    display_name varchar(100) NOT NULL,
    description text,
    is_required boolean DEFAULT true,
    validation_rules jsonb DEFAULT '{}',
    created_at timestamp with time zone DEFAULT now()
);

-- Insert default document types
INSERT INTO kyc_document_types (type_code, display_name, description, is_required) VALUES
('national_id', 'National Identity Card', 'Government issued national identity card', true),
('passport', 'Passport', 'International passport document', true),
('driving_license', 'Driving License', 'Government issued driving license', true),
('selfie', 'Selfie Photo', 'Clear selfie photo for identity verification', true),
('address_proof', 'Address Proof', 'Utility bill or bank statement for address verification', true)
ON CONFLICT (type_code) DO NOTHING;

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_user_id ON kyc_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_status ON kyc_submissions(status);
CREATE INDEX IF NOT EXISTS idx_kyc_submissions_created_at ON kyc_submissions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_kyc_status_history_user_id ON kyc_status_history(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_status_history_submission_id ON kyc_status_history(kyc_submission_id);

-- 7. Create function to update KYC status in users table
CREATE OR REPLACE FUNCTION update_user_kyc_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user's KYC status when submission status changes
    UPDATE users 
    SET 
        kyc_status = NEW.status,
        kyc_submitted_at = CASE 
            WHEN NEW.status IN ('pending', 'under_review') AND OLD.status = 'not_submitted' 
            THEN NEW.created_at 
            ELSE kyc_submitted_at 
        END,
        kyc_approved_at = CASE 
            WHEN NEW.status = 'approved' 
            THEN NEW.reviewed_at 
            ELSE NULL 
        END
    WHERE id = NEW.user_id;
    
    -- Insert status history record
    INSERT INTO kyc_status_history (
        kyc_submission_id, 
        user_id, 
        previous_status, 
        new_status, 
        changed_by, 
        change_reason,
        admin_notes
    ) VALUES (
        NEW.id, 
        NEW.user_id, 
        OLD.status, 
        NEW.status, 
        NEW.reviewed_by,
        CASE 
            WHEN NEW.status = 'rejected' THEN NEW.rejection_reason
            ELSE 'Status updated'
        END,
        NEW.admin_notes
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Create trigger for automatic status updates
CREATE TRIGGER kyc_status_update_trigger
    AFTER UPDATE OF status ON kyc_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_kyc_status();

-- 9. Create function to check if user is KYC verified
CREATE OR REPLACE FUNCTION is_kyc_verified(user_uuid uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_uuid 
        AND kyc_status = 'approved'
    );
END;
$$ LANGUAGE plpgsql;

-- 10. Enable RLS on KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for KYC tables
-- Users can only see their own KYC submissions
CREATE POLICY "Users can view own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can view all KYC submissions
CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Users can view their own KYC history
CREATE POLICY "Users can view own KYC history" ON kyc_status_history
    FOR SELECT USING (auth.uid() = user_id);

-- Admins can view all KYC history
CREATE POLICY "Admins can view all KYC history" ON kyc_status_history
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Everyone can read document types (reference data)
CREATE POLICY "Everyone can read document types" ON kyc_document_types
    FOR SELECT USING (true);

-- Only admins can modify document types
CREATE POLICY "Admins can modify document types" ON kyc_document_types
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- 12. Create updated_at trigger for KYC tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_kyc_submissions_updated_at
    BEFORE UPDATE ON kyc_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Success message
SELECT 'KYC system tables created successfully!' as result;
