"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/adminSettings.ts":
/*!*******************************************!*\
  !*** ./src/lib/services/adminSettings.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminSettingsService: function() { return /* binding */ AdminSettingsService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass AdminSettingsService {\n    /**\n   * Get all admin settings with fallback to defaults\n   */ static async getSettings() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"key, value\");\n            if (error) {\n                console.warn(\"Failed to fetch settings, using defaults:\", error.message);\n                return this.getDefaultSettings();\n            }\n            // Convert array of key-value pairs to object\n            const settingsMap = {};\n            data === null || data === void 0 ? void 0 : data.forEach((setting)=>{\n                settingsMap[setting.key] = setting.value;\n            });\n            var _settingsMap_allow_registration, _settingsMap_require_email_verification, _settingsMap_auto_approve_ads, _settingsMap_max_images_per_ad, _settingsMap_ad_expiry_days, _settingsMap_enable_notifications, _settingsMap_maintenance_mode, _settingsMap_enable_analytics, _settingsMap_enable_referrals, _settingsMap_free_ads_limit;\n            // Return settings with proper types and defaults\n            return {\n                siteName: settingsMap.site_name || \"OKDOI\",\n                siteDescription: settingsMap.site_description || \"Premium Marketplace for Everything\",\n                adminEmail: settingsMap.admin_email || \"<EMAIL>\",\n                allowRegistration: (_settingsMap_allow_registration = settingsMap.allow_registration) !== null && _settingsMap_allow_registration !== void 0 ? _settingsMap_allow_registration : true,\n                requireEmailVerification: (_settingsMap_require_email_verification = settingsMap.require_email_verification) !== null && _settingsMap_require_email_verification !== void 0 ? _settingsMap_require_email_verification : false,\n                autoApproveAds: (_settingsMap_auto_approve_ads = settingsMap.auto_approve_ads) !== null && _settingsMap_auto_approve_ads !== void 0 ? _settingsMap_auto_approve_ads : false,\n                maxImagesPerAd: (_settingsMap_max_images_per_ad = settingsMap.max_images_per_ad) !== null && _settingsMap_max_images_per_ad !== void 0 ? _settingsMap_max_images_per_ad : 10,\n                adExpiryDays: (_settingsMap_ad_expiry_days = settingsMap.ad_expiry_days) !== null && _settingsMap_ad_expiry_days !== void 0 ? _settingsMap_ad_expiry_days : 30,\n                enableNotifications: (_settingsMap_enable_notifications = settingsMap.enable_notifications) !== null && _settingsMap_enable_notifications !== void 0 ? _settingsMap_enable_notifications : true,\n                maintenanceMode: (_settingsMap_maintenance_mode = settingsMap.maintenance_mode) !== null && _settingsMap_maintenance_mode !== void 0 ? _settingsMap_maintenance_mode : false,\n                enableAnalytics: (_settingsMap_enable_analytics = settingsMap.enable_analytics) !== null && _settingsMap_enable_analytics !== void 0 ? _settingsMap_enable_analytics : true,\n                enableReferrals: (_settingsMap_enable_referrals = settingsMap.enable_referrals) !== null && _settingsMap_enable_referrals !== void 0 ? _settingsMap_enable_referrals : true,\n                freeAdsLimit: (_settingsMap_free_ads_limit = settingsMap.free_ads_limit) !== null && _settingsMap_free_ads_limit !== void 0 ? _settingsMap_free_ads_limit : 2\n            };\n        } catch (error) {\n            console.warn(\"Error fetching settings, using defaults:\", error);\n            return this.getDefaultSettings();\n        }\n    }\n    /**\n   * Get default settings when table is not available\n   */ static getDefaultSettings() {\n        return {\n            siteName: \"OKDOI\",\n            siteDescription: \"Premium Marketplace for Everything\",\n            adminEmail: \"<EMAIL>\",\n            allowRegistration: true,\n            requireEmailVerification: false,\n            autoApproveAds: false,\n            maxImagesPerAd: 10,\n            adExpiryDays: 30,\n            enableNotifications: true,\n            maintenanceMode: false,\n            enableAnalytics: true,\n            enableReferrals: true,\n            freeAdsLimit: 2\n        };\n    }\n    /**\n   * Update admin settings\n   */ static async updateSettings(settings) {\n        const updates = [];\n        // Convert settings object to database format\n        if (settings.siteName !== undefined) {\n            updates.push({\n                key: \"site_name\",\n                value: settings.siteName\n            });\n        }\n        if (settings.siteDescription !== undefined) {\n            updates.push({\n                key: \"site_description\",\n                value: settings.siteDescription\n            });\n        }\n        if (settings.adminEmail !== undefined) {\n            updates.push({\n                key: \"admin_email\",\n                value: settings.adminEmail\n            });\n        }\n        if (settings.allowRegistration !== undefined) {\n            updates.push({\n                key: \"allow_registration\",\n                value: settings.allowRegistration\n            });\n        }\n        if (settings.requireEmailVerification !== undefined) {\n            updates.push({\n                key: \"require_email_verification\",\n                value: settings.requireEmailVerification\n            });\n        }\n        if (settings.autoApproveAds !== undefined) {\n            updates.push({\n                key: \"auto_approve_ads\",\n                value: settings.autoApproveAds\n            });\n        }\n        if (settings.maxImagesPerAd !== undefined) {\n            updates.push({\n                key: \"max_images_per_ad\",\n                value: settings.maxImagesPerAd\n            });\n        }\n        if (settings.adExpiryDays !== undefined) {\n            updates.push({\n                key: \"ad_expiry_days\",\n                value: settings.adExpiryDays\n            });\n        }\n        if (settings.enableNotifications !== undefined) {\n            updates.push({\n                key: \"enable_notifications\",\n                value: settings.enableNotifications\n            });\n        }\n        if (settings.maintenanceMode !== undefined) {\n            updates.push({\n                key: \"maintenance_mode\",\n                value: settings.maintenanceMode\n            });\n        }\n        if (settings.enableAnalytics !== undefined) {\n            updates.push({\n                key: \"enable_analytics\",\n                value: settings.enableAnalytics\n            });\n        }\n        if (settings.enableReferrals !== undefined) {\n            updates.push({\n                key: \"enable_referrals\",\n                value: settings.enableReferrals\n            });\n        }\n        if (settings.freeAdsLimit !== undefined) {\n            updates.push({\n                key: \"free_ads_limit\",\n                value: settings.freeAdsLimit\n            });\n        }\n        // Update each setting\n        for (const update of updates){\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n                key: update.key,\n                value: update.value,\n                updated_at: new Date().toISOString()\n            });\n            if (error) {\n                throw new Error(\"Failed to update setting \".concat(update.key, \": \").concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get a specific setting value with fallback defaults\n   */ static async getSetting(key) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"value\").eq(\"key\", key).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return this.getDefaultSetting(key) // Setting not found, return default\n                    ;\n                }\n                // If table doesn't exist or other error, return default\n                console.warn(\"Error fetching setting \".concat(key, \":\"), error.message);\n                return this.getDefaultSetting(key);\n            }\n            return data === null || data === void 0 ? void 0 : data.value;\n        } catch (error) {\n            console.warn(\"Error fetching setting \".concat(key, \":\"), error);\n            return this.getDefaultSetting(key);\n        }\n    }\n    /**\n   * Get default value for a setting\n   */ static getDefaultSetting(key) {\n        const defaults = {\n            \"site_name\": \"OKDOI\",\n            \"site_description\": \"Premium Marketplace for Everything\",\n            \"admin_email\": \"<EMAIL>\",\n            \"allow_registration\": true,\n            \"require_email_verification\": false,\n            \"auto_approve_ads\": false,\n            \"max_images_per_ad\": 10,\n            \"ad_expiry_days\": 30,\n            \"enable_notifications\": true,\n            \"maintenance_mode\": false,\n            \"enable_analytics\": true,\n            \"enable_referrals\": true,\n            \"free_ads_limit\": 2\n        };\n        return defaults[key] || null;\n    }\n    /**\n   * Update a specific setting\n   */ static async updateSetting(key, value) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n            key,\n            value,\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: \"key\",\n            ignoreDuplicates: false\n        });\n        if (error) {\n            throw new Error(\"Failed to update setting \".concat(key, \": \").concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/adminSettings.ts\n"));

/***/ })

});