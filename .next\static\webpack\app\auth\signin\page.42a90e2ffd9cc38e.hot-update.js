"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/lib/services/adminSettings.ts":
/*!*******************************************!*\
  !*** ./src/lib/services/adminSettings.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminSettingsService: function() { return /* binding */ AdminSettingsService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass AdminSettingsService {\n    /**\n   * Get all admin settings with fallback to defaults\n   */ static async getSettings() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"key, value\");\n            if (error) {\n                console.warn(\"Failed to fetch settings, using defaults:\", error.message);\n                return this.getDefaultSettings();\n            }\n            // Convert array of key-value pairs to object\n            const settingsMap = {};\n            data === null || data === void 0 ? void 0 : data.forEach((setting)=>{\n                settingsMap[setting.key] = setting.value;\n            });\n            var _settingsMap_allow_registration, _settingsMap_require_email_verification, _settingsMap_auto_approve_ads, _settingsMap_max_images_per_ad, _settingsMap_ad_expiry_days, _settingsMap_enable_notifications, _settingsMap_maintenance_mode, _settingsMap_enable_analytics, _settingsMap_enable_referrals, _settingsMap_free_ads_limit;\n            // Return settings with proper types and defaults\n            return {\n                siteName: settingsMap.site_name || \"OKDOI\",\n                siteDescription: settingsMap.site_description || \"Premium Marketplace for Everything\",\n                adminEmail: settingsMap.admin_email || \"<EMAIL>\",\n                allowRegistration: (_settingsMap_allow_registration = settingsMap.allow_registration) !== null && _settingsMap_allow_registration !== void 0 ? _settingsMap_allow_registration : true,\n                requireEmailVerification: (_settingsMap_require_email_verification = settingsMap.require_email_verification) !== null && _settingsMap_require_email_verification !== void 0 ? _settingsMap_require_email_verification : false,\n                autoApproveAds: (_settingsMap_auto_approve_ads = settingsMap.auto_approve_ads) !== null && _settingsMap_auto_approve_ads !== void 0 ? _settingsMap_auto_approve_ads : false,\n                maxImagesPerAd: (_settingsMap_max_images_per_ad = settingsMap.max_images_per_ad) !== null && _settingsMap_max_images_per_ad !== void 0 ? _settingsMap_max_images_per_ad : 10,\n                adExpiryDays: (_settingsMap_ad_expiry_days = settingsMap.ad_expiry_days) !== null && _settingsMap_ad_expiry_days !== void 0 ? _settingsMap_ad_expiry_days : 30,\n                enableNotifications: (_settingsMap_enable_notifications = settingsMap.enable_notifications) !== null && _settingsMap_enable_notifications !== void 0 ? _settingsMap_enable_notifications : true,\n                maintenanceMode: (_settingsMap_maintenance_mode = settingsMap.maintenance_mode) !== null && _settingsMap_maintenance_mode !== void 0 ? _settingsMap_maintenance_mode : false,\n                enableAnalytics: (_settingsMap_enable_analytics = settingsMap.enable_analytics) !== null && _settingsMap_enable_analytics !== void 0 ? _settingsMap_enable_analytics : true,\n                enableReferrals: (_settingsMap_enable_referrals = settingsMap.enable_referrals) !== null && _settingsMap_enable_referrals !== void 0 ? _settingsMap_enable_referrals : true,\n                freeAdsLimit: (_settingsMap_free_ads_limit = settingsMap.free_ads_limit) !== null && _settingsMap_free_ads_limit !== void 0 ? _settingsMap_free_ads_limit : 2\n            };\n        } catch (error) {\n            console.warn(\"Error fetching settings, using defaults:\", error);\n            return this.getDefaultSettings();\n        }\n    }\n    /**\n   * Get default settings when table is not available\n   */ static getDefaultSettings() {\n        return {\n            siteName: \"OKDOI\",\n            siteDescription: \"Premium Marketplace for Everything\",\n            adminEmail: \"<EMAIL>\",\n            allowRegistration: true,\n            requireEmailVerification: false,\n            autoApproveAds: false,\n            maxImagesPerAd: 10,\n            adExpiryDays: 30,\n            enableNotifications: true,\n            maintenanceMode: false,\n            enableAnalytics: true,\n            enableReferrals: true,\n            freeAdsLimit: 2\n        };\n    }\n    /**\n   * Update admin settings\n   */ static async updateSettings(settings) {\n        const updates = [];\n        // Convert settings object to database format\n        if (settings.siteName !== undefined) {\n            updates.push({\n                key: \"site_name\",\n                value: settings.siteName\n            });\n        }\n        if (settings.siteDescription !== undefined) {\n            updates.push({\n                key: \"site_description\",\n                value: settings.siteDescription\n            });\n        }\n        if (settings.adminEmail !== undefined) {\n            updates.push({\n                key: \"admin_email\",\n                value: settings.adminEmail\n            });\n        }\n        if (settings.allowRegistration !== undefined) {\n            updates.push({\n                key: \"allow_registration\",\n                value: settings.allowRegistration\n            });\n        }\n        if (settings.requireEmailVerification !== undefined) {\n            updates.push({\n                key: \"require_email_verification\",\n                value: settings.requireEmailVerification\n            });\n        }\n        if (settings.autoApproveAds !== undefined) {\n            updates.push({\n                key: \"auto_approve_ads\",\n                value: settings.autoApproveAds\n            });\n        }\n        if (settings.maxImagesPerAd !== undefined) {\n            updates.push({\n                key: \"max_images_per_ad\",\n                value: settings.maxImagesPerAd\n            });\n        }\n        if (settings.adExpiryDays !== undefined) {\n            updates.push({\n                key: \"ad_expiry_days\",\n                value: settings.adExpiryDays\n            });\n        }\n        if (settings.enableNotifications !== undefined) {\n            updates.push({\n                key: \"enable_notifications\",\n                value: settings.enableNotifications\n            });\n        }\n        if (settings.maintenanceMode !== undefined) {\n            updates.push({\n                key: \"maintenance_mode\",\n                value: settings.maintenanceMode\n            });\n        }\n        if (settings.enableAnalytics !== undefined) {\n            updates.push({\n                key: \"enable_analytics\",\n                value: settings.enableAnalytics\n            });\n        }\n        if (settings.enableReferrals !== undefined) {\n            updates.push({\n                key: \"enable_referrals\",\n                value: settings.enableReferrals\n            });\n        }\n        if (settings.freeAdsLimit !== undefined) {\n            updates.push({\n                key: \"free_ads_limit\",\n                value: settings.freeAdsLimit\n            });\n        }\n        // Update each setting with proper conflict resolution\n        for (const update of updates){\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n                key: update.key,\n                value: update.value,\n                updated_at: new Date().toISOString()\n            }, {\n                onConflict: \"key\",\n                ignoreDuplicates: false\n            });\n            if (error) {\n                throw new Error(\"Failed to update setting \".concat(update.key, \": \").concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get a specific setting value with fallback defaults\n   */ static async getSetting(key) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"value\").eq(\"key\", key).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return this.getDefaultSetting(key) // Setting not found, return default\n                    ;\n                }\n                // If table doesn't exist or other error, return default\n                console.warn(\"Error fetching setting \".concat(key, \":\"), error.message);\n                return this.getDefaultSetting(key);\n            }\n            return data === null || data === void 0 ? void 0 : data.value;\n        } catch (error) {\n            console.warn(\"Error fetching setting \".concat(key, \":\"), error);\n            return this.getDefaultSetting(key);\n        }\n    }\n    /**\n   * Get default value for a setting\n   */ static getDefaultSetting(key) {\n        const defaults = {\n            \"site_name\": \"OKDOI\",\n            \"site_description\": \"Premium Marketplace for Everything\",\n            \"admin_email\": \"<EMAIL>\",\n            \"allow_registration\": true,\n            \"require_email_verification\": false,\n            \"auto_approve_ads\": false,\n            \"max_images_per_ad\": 10,\n            \"ad_expiry_days\": 30,\n            \"enable_notifications\": true,\n            \"maintenance_mode\": false,\n            \"enable_analytics\": true,\n            \"enable_referrals\": true,\n            \"free_ads_limit\": 2\n        };\n        return defaults[key] || null;\n    }\n    /**\n   * Update a specific setting\n   */ static async updateSetting(key, value) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n            key,\n            value,\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: \"key\",\n            ignoreDuplicates: false\n        });\n        if (error) {\n            throw new Error(\"Failed to update setting \".concat(key, \": \").concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/adminSettings.ts\n"));

/***/ })

});