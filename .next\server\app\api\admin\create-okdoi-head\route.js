"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/create-okdoi-head/route";
exports.ids = ["app/api/admin/create-okdoi-head/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/create-okdoi-head/route.ts */ \"(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/create-okdoi-head/route\",\n        pathname: \"/api/admin/create-okdoi-head\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/create-okdoi-head/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\create-okdoi-head\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/create-okdoi-head/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/create-okdoi-head/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/admin/create-okdoi-head/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(rsc)/./src/lib/services/referralSystem.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin using admin client\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Check if OKDOI Head already exists\n        const existingHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        if (existingHead) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"OKDOI Head already exists\",\n                data: existingHead\n            }, {\n                status: 400\n            });\n        }\n        // Parse request body for custom details (optional)\n        const body = await request.json().catch(()=>({}));\n        const { email, fullName, phone, userId } = body;\n        let okdoiHead;\n        if (userId) {\n            // Assign OKDOI Head to existing user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.assignOKDOIHeadToUser(userId);\n        } else {\n            // Create new OKDOI Head user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.createOKDOIHead(email || \"<EMAIL>\", fullName || \"OKDOI Head\", phone);\n        }\n        console.log(`Admin ${user.email} created OKDOI Head: ${okdoiHead.email}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"OKDOI Head created successfully\",\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error creating OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get OKDOI Head user\n        const okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error getting OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/referralSystem.ts":
/*!********************************************!*\
  !*** ./src/lib/services/referralSystem.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReferralSystemService: () => (/* binding */ ReferralSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ReferralSystemService - Manages the multi-level referral and commission system\n */ class ReferralSystemService {\n    /**\n   * Generate a unique referral code\n   */ static async generateReferralCode() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"generate_referral_code\");\n        if (error) {\n            throw new Error(`Failed to generate referral code: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Validate referral code and get referrer information\n   */ static async validateReferralCode(code) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"referral_code\", code).eq(\"is_referral_active\", true).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Code not found\n                    ;\n                }\n                throw new Error(`Failed to validate referral code: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error validating referral code:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place user in referral hierarchy\n   */ static async placeUserInHierarchy(newUserId, referrerId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"place_user_in_hierarchy\", {\n                new_user_id: newUserId,\n                referrer_id: referrerId\n            });\n            if (error) {\n                throw new Error(`Failed to place user in hierarchy: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error placing user in hierarchy:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral statistics\n   */ static async getUserReferralStats(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"direct_referrals_count, total_downline_count, total_commission_earned, referral_level, referral_code\").eq(\"id\", userId).single();\n            if (error) {\n                throw new Error(`Failed to get referral stats: ${error.message}`);\n            }\n            let referralCode = data.referral_code;\n            // If user doesn't have a referral code, generate one\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n                // Update the user with the new referral code\n                const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                    referral_code: referralCode\n                }).eq(\"id\", userId);\n                if (updateError) {\n                    console.error(\"Error updating referral code:\", updateError);\n                // Don't throw error, just use the generated code\n                }\n            }\n            return {\n                directReferrals: data.direct_referrals_count || 0,\n                totalDownline: data.total_downline_count || 0,\n                totalCommissionEarned: data.total_commission_earned || 0,\n                currentLevel: data.referral_level || 0,\n                totalReferrals: data.direct_referrals_count || 0,\n                referralCode: referralCode\n            };\n        } catch (error) {\n            console.error(\"Error getting referral stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's direct referrals\n   */ static async getDirectReferrals(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n          child_id,\n          position,\n          created_at,\n          child:child_id(*)\n        `).eq(\"parent_id\", userId).order(\"position\");\n            if (error) {\n                throw new Error(`Failed to get direct referrals: ${error.message}`);\n            }\n            return data?.map((item)=>item.child) || [];\n        } catch (error) {\n            console.error(\"Error getting direct referrals:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral tree (up to specified depth)\n   */ static async getReferralTree(userId, maxDepth = 3) {\n        try {\n            // Get the root user\n            const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (rootError) {\n                throw new Error(`Failed to get root user: ${rootError.message}`);\n            }\n            // Build the tree recursively\n            const buildTree = async (user, currentDepth)=>{\n                const children = [];\n                if (currentDepth < maxDepth) {\n                    const { data: placements, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n              position,\n              child:child_id(*)\n            `).eq(\"parent_id\", user.id).order(\"position\");\n                    if (!error && placements) {\n                        for (const placement of placements){\n                            const childNode = await buildTree(placement.child, currentDepth + 1);\n                            childNode.position = placement.position;\n                            children.push(childNode);\n                        }\n                    }\n                }\n                return {\n                    user,\n                    children,\n                    level: currentDepth,\n                    position: 0 // Will be set by parent\n                };\n            };\n            return await buildTree(rootUser, 0);\n        } catch (error) {\n            console.error(\"Error getting referral tree:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's commission transactions\n   */ static async getCommissionTransactions(userId, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"beneficiary_id\", userId)\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create Zonal Manager\n   */ static async createZonalManager(userId, zoneName, zoneDescription, assignedDistricts = [], createdBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"zonal_manager\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create zonal manager record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).insert({\n                user_id: userId,\n                zone_name: zoneName,\n                zone_description: zoneDescription,\n                assigned_districts: assignedDistricts,\n                created_by: createdBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create zonal manager: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upgrade user to RSM\n   */ static async upgradeToRSM(userId, zonalManagerId, regionName, upgradedBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"rsm\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create RSM record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).insert({\n                user_id: userId,\n                zonal_manager_id: zonalManagerId,\n                region_name: regionName,\n                upgraded_by: upgradedBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create RSM: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error upgrading to RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Zonal Managers\n   */ static async getZonalManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(`\n          *,\n          user:user_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get zonal managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting zonal managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Regional Sales Managers\n   */ static async getRegionalSalesManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          *,\n          user:user_id(*),\n          zonal_manager:zonal_manager_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get regional sales managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting regional sales managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search users for ZM/RSM creation\n   */ static async searchUsers(searchTerm, limit = 20) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`).eq(\"user_type\", \"user\") // Only regular users can be upgraded\n            .limit(limit);\n            if (error) {\n                throw new Error(`Failed to search users: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get referral system statistics\n   */ static async getReferralSystemStats() {\n        try {\n            const [totalUsersResult, zonalManagersResult, regionalManagersResult, totalReferralsResult, activeCodesResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).not(\"referral_code\", \"is\", null)\n            ]);\n            return {\n                totalUsers: totalUsersResult.count || 0,\n                zonalManagers: zonalManagersResult.count || 0,\n                regionalManagers: regionalManagersResult.count || 0,\n                totalReferrals: totalReferralsResult.count || 0,\n                activeReferralCodes: activeCodesResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting referral system stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Zonal Manager\n   */ static async deactivateZonalManager(zmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", zmId);\n            if (error) {\n                throw new Error(`Failed to deactivate zonal manager: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Regional Sales Manager\n   */ static async deactivateRSM(rsmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", rsmId);\n            if (error) {\n                throw new Error(`Failed to deactivate RSM: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create or get OKDOI Head user\n   */ static async createOKDOIHead(email = \"<EMAIL>\", fullName = \"OKDOI Head\", phone) {\n        try {\n            // Check if OKDOI Head already exists using admin client\n            const { data: existingHead, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (existingHead && !checkError) {\n                return existingHead;\n            }\n            // Generate referral code first\n            const referralCode = await this.generateReferralCode();\n            // Create auth user first using admin auth API\n            const { data: authUser, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.createUser({\n                email,\n                password: crypto.randomUUID(),\n                email_confirm: true,\n                user_metadata: {\n                    full_name: fullName,\n                    phone: phone || null\n                }\n            });\n            if (authError || !authUser.user) {\n                throw new Error(`Failed to create auth user: ${authError?.message}`);\n            }\n            // Create OKDOI Head user in public.users table using the auth user ID\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).insert({\n                id: authUser.user.id,\n                email,\n                full_name: fullName,\n                phone,\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                // If user creation fails, clean up the auth user\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(authUser.user.id);\n                throw new Error(`Failed to create OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Assign OKDOI Head role to existing user\n   */ static async assignOKDOIHeadToUser(userId) {\n        try {\n            // Check if OKDOI Head already exists\n            const existingHead = await this.getOKDOIHead();\n            if (existingHead) {\n                throw new Error(\"OKDOI Head already exists. Only one OKDOI Head is allowed.\");\n            }\n            // Get the user to be assigned\n            const { data: user, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (userError || !user) {\n                throw new Error(\"User not found\");\n            }\n            // Generate referral code if user doesn't have one\n            let referralCode = user.referral_code;\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n            }\n            // Update user to OKDOI Head\n            const { data: updatedUser, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", userId).select().single();\n            if (updateError) {\n                throw new Error(`Failed to assign OKDOI Head: ${updateError.message}`);\n            }\n            return updatedUser;\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get OKDOI Head user\n   */ static async getOKDOIHead() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No OKDOI Head found\n                    ;\n                }\n                throw new Error(`Failed to get OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting OKDOI Head:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/referralSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();