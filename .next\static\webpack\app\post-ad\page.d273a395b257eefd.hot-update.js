"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/post-ad/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    console.warn(\"Invalid referral code provided, continuing without referrer\");\n                // Don't fail signup for invalid referral code\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // If user is created and email verification is NOT required, create profile immediately\n        if (data.user && !requireEmailVerification) {\n            try {\n                await this.createUserProfile(data.user, userData, referrer);\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile in public.users table\n   */ static async createUserProfile(authUser, userData, referrer) {\n        // Check if admin client is available\n        if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n            throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n        }\n        // Use service role client to bypass RLS for user creation\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n            id: authUser.id,\n            email: authUser.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n            location: userData === null || userData === void 0 ? void 0 : userData.location,\n            user_type: \"user\",\n            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n            referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n        });\n        if (profileError) {\n            throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n        }\n        // If user has a referrer, place them in hierarchy\n        if (referrer) {\n            try {\n                await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n            } catch (referralError) {\n                console.error(\"Failed to place user in referral hierarchy:\", referralError);\n            // Don't fail the profile creation, but log the error\n            }\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});