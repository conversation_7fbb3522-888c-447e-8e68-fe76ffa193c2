"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n        }\n        // Validate referral code if provided\n        let referrer = null;\n        if (userData === null || userData === void 0 ? void 0 : userData.referralCode) {\n            try {\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(userData.referralCode);\n                if (!referrer) {\n                    throw new Error(\"Invalid referral code\");\n                }\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: userData === null || userData === void 0 ? void 0 : userData.referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // If user is created and email verification is NOT required, create profile immediately\n        if (data.user && !requireEmailVerification) {\n            try {\n                await this.createUserProfile(data.user, userData, referrer);\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile in public.users table\n   */ static async createUserProfile(authUser, userData, referrer) {\n        try {\n            // Check if admin client is available\n            if (!_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                console.error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n                throw new Error(\"Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY\");\n            }\n            // Use service role client to bypass RLS for user creation\n            const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"users\").insert({\n                id: authUser.id,\n                email: authUser.email,\n                full_name: (userData === null || userData === void 0 ? void 0 : userData.full_name) || null,\n                phone: (userData === null || userData === void 0 ? void 0 : userData.phone) || null,\n                location: (userData === null || userData === void 0 ? void 0 : userData.location) || null,\n                user_type: \"user\",\n                referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null,\n                is_verified: false,\n                role: \"user\"\n            });\n            if (profileError) {\n                console.error(\"Profile creation error:\", profileError);\n                throw new Error(\"Failed to create user profile: \".concat(profileError.message));\n            }\n            console.log(\"User profile created successfully for:\", authUser.email);\n            // If user has a referrer, place them in hierarchy\n            if (referrer) {\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                    console.log(\"User placed in referral hierarchy successfully\");\n                } catch (referralError) {\n                    console.error(\"Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in createUserProfile:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata, _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3, _data_user_user_metadata4;\n                    // Get referrer if referral code exists\n                    let referrer = null;\n                    if ((_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code) {\n                        referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(data.user.user_metadata.referral_code);\n                    }\n                    // Create user profile\n                    await this.createUserProfile(data.user, {\n                        full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                        phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                        location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                        referralCode: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.referral_code\n                    }, referrer);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});