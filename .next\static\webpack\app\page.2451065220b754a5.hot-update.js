"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/services/adminSettings.ts":
/*!*******************************************!*\
  !*** ./src/lib/services/adminSettings.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminSettingsService: function() { return /* binding */ AdminSettingsService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass AdminSettingsService {\n    /**\n   * Get all admin settings with fallback to defaults\n   */ static async getSettings() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"key, value\");\n            if (error) {\n                console.warn(\"Failed to fetch settings, using defaults:\", error.message);\n                return this.getDefaultSettings();\n            }\n            // Convert array of key-value pairs to object\n            const settingsMap = {};\n            data === null || data === void 0 ? void 0 : data.forEach((setting)=>{\n                settingsMap[setting.key] = setting.value;\n            });\n            var _settingsMap_allow_registration, _settingsMap_require_email_verification, _settingsMap_auto_approve_ads, _settingsMap_max_images_per_ad, _settingsMap_ad_expiry_days, _settingsMap_enable_notifications, _settingsMap_maintenance_mode, _settingsMap_enable_analytics, _settingsMap_enable_referrals, _settingsMap_free_ads_limit;\n            // Return settings with proper types and defaults\n            return {\n                siteName: settingsMap.site_name || \"OKDOI\",\n                siteDescription: settingsMap.site_description || \"Premium Marketplace for Everything\",\n                adminEmail: settingsMap.admin_email || \"<EMAIL>\",\n                allowRegistration: (_settingsMap_allow_registration = settingsMap.allow_registration) !== null && _settingsMap_allow_registration !== void 0 ? _settingsMap_allow_registration : true,\n                requireEmailVerification: (_settingsMap_require_email_verification = settingsMap.require_email_verification) !== null && _settingsMap_require_email_verification !== void 0 ? _settingsMap_require_email_verification : false,\n                autoApproveAds: (_settingsMap_auto_approve_ads = settingsMap.auto_approve_ads) !== null && _settingsMap_auto_approve_ads !== void 0 ? _settingsMap_auto_approve_ads : false,\n                maxImagesPerAd: (_settingsMap_max_images_per_ad = settingsMap.max_images_per_ad) !== null && _settingsMap_max_images_per_ad !== void 0 ? _settingsMap_max_images_per_ad : 10,\n                adExpiryDays: (_settingsMap_ad_expiry_days = settingsMap.ad_expiry_days) !== null && _settingsMap_ad_expiry_days !== void 0 ? _settingsMap_ad_expiry_days : 30,\n                enableNotifications: (_settingsMap_enable_notifications = settingsMap.enable_notifications) !== null && _settingsMap_enable_notifications !== void 0 ? _settingsMap_enable_notifications : true,\n                maintenanceMode: (_settingsMap_maintenance_mode = settingsMap.maintenance_mode) !== null && _settingsMap_maintenance_mode !== void 0 ? _settingsMap_maintenance_mode : false,\n                enableAnalytics: (_settingsMap_enable_analytics = settingsMap.enable_analytics) !== null && _settingsMap_enable_analytics !== void 0 ? _settingsMap_enable_analytics : true,\n                enableReferrals: (_settingsMap_enable_referrals = settingsMap.enable_referrals) !== null && _settingsMap_enable_referrals !== void 0 ? _settingsMap_enable_referrals : true,\n                freeAdsLimit: (_settingsMap_free_ads_limit = settingsMap.free_ads_limit) !== null && _settingsMap_free_ads_limit !== void 0 ? _settingsMap_free_ads_limit : 2\n            };\n        } catch (error) {\n            console.warn(\"Error fetching settings, using defaults:\", error);\n            return this.getDefaultSettings();\n        }\n    }\n    /**\n   * Get default settings when table is not available\n   */ static getDefaultSettings() {\n        return {\n            siteName: \"OKDOI\",\n            siteDescription: \"Premium Marketplace for Everything\",\n            adminEmail: \"<EMAIL>\",\n            allowRegistration: true,\n            requireEmailVerification: false,\n            autoApproveAds: false,\n            maxImagesPerAd: 10,\n            adExpiryDays: 30,\n            enableNotifications: true,\n            maintenanceMode: false,\n            enableAnalytics: true,\n            enableReferrals: true,\n            freeAdsLimit: 2\n        };\n    }\n    /**\n   * Update admin settings\n   */ static async updateSettings(settings) {\n        const updates = [];\n        // Convert settings object to database format\n        if (settings.siteName !== undefined) {\n            updates.push({\n                key: \"site_name\",\n                value: settings.siteName\n            });\n        }\n        if (settings.siteDescription !== undefined) {\n            updates.push({\n                key: \"site_description\",\n                value: settings.siteDescription\n            });\n        }\n        if (settings.adminEmail !== undefined) {\n            updates.push({\n                key: \"admin_email\",\n                value: settings.adminEmail\n            });\n        }\n        if (settings.allowRegistration !== undefined) {\n            updates.push({\n                key: \"allow_registration\",\n                value: settings.allowRegistration\n            });\n        }\n        if (settings.requireEmailVerification !== undefined) {\n            updates.push({\n                key: \"require_email_verification\",\n                value: settings.requireEmailVerification\n            });\n        }\n        if (settings.autoApproveAds !== undefined) {\n            updates.push({\n                key: \"auto_approve_ads\",\n                value: settings.autoApproveAds\n            });\n        }\n        if (settings.maxImagesPerAd !== undefined) {\n            updates.push({\n                key: \"max_images_per_ad\",\n                value: settings.maxImagesPerAd\n            });\n        }\n        if (settings.adExpiryDays !== undefined) {\n            updates.push({\n                key: \"ad_expiry_days\",\n                value: settings.adExpiryDays\n            });\n        }\n        if (settings.enableNotifications !== undefined) {\n            updates.push({\n                key: \"enable_notifications\",\n                value: settings.enableNotifications\n            });\n        }\n        if (settings.maintenanceMode !== undefined) {\n            updates.push({\n                key: \"maintenance_mode\",\n                value: settings.maintenanceMode\n            });\n        }\n        if (settings.enableAnalytics !== undefined) {\n            updates.push({\n                key: \"enable_analytics\",\n                value: settings.enableAnalytics\n            });\n        }\n        if (settings.enableReferrals !== undefined) {\n            updates.push({\n                key: \"enable_referrals\",\n                value: settings.enableReferrals\n            });\n        }\n        if (settings.freeAdsLimit !== undefined) {\n            updates.push({\n                key: \"free_ads_limit\",\n                value: settings.freeAdsLimit\n            });\n        }\n        // Update each setting\n        for (const update of updates){\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n                key: update.key,\n                value: update.value,\n                updated_at: new Date().toISOString()\n            });\n            if (error) {\n                throw new Error(\"Failed to update setting \".concat(update.key, \": \").concat(error.message));\n            }\n        }\n    }\n    /**\n   * Get a specific setting value with fallback defaults\n   */ static async getSetting(key) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").select(\"value\").eq(\"key\", key).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return this.getDefaultSetting(key) // Setting not found, return default\n                    ;\n                }\n                // If table doesn't exist or other error, return default\n                console.warn(\"Error fetching setting \".concat(key, \":\"), error.message);\n                return this.getDefaultSetting(key);\n            }\n            return data === null || data === void 0 ? void 0 : data.value;\n        } catch (error) {\n            console.warn(\"Error fetching setting \".concat(key, \":\"), error);\n            return this.getDefaultSetting(key);\n        }\n    }\n    /**\n   * Get default value for a setting\n   */ static getDefaultSetting(key) {\n        const defaults = {\n            \"site_name\": \"OKDOI\",\n            \"site_description\": \"Premium Marketplace for Everything\",\n            \"admin_email\": \"<EMAIL>\",\n            \"allow_registration\": true,\n            \"require_email_verification\": false,\n            \"auto_approve_ads\": false,\n            \"max_images_per_ad\": 10,\n            \"ad_expiry_days\": 30,\n            \"enable_notifications\": true,\n            \"maintenance_mode\": false,\n            \"enable_analytics\": true,\n            \"enable_referrals\": true,\n            \"free_ads_limit\": 2\n        };\n        return defaults[key] || null;\n    }\n    /**\n   * Update a specific setting\n   */ static async updateSetting(key, value) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_settings\").upsert({\n            key,\n            value,\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: \"key\",\n            ignoreDuplicates: false\n        });\n        if (error) {\n            throw new Error(\"Failed to update setting \".concat(key, \": \").concat(error.message));\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/adminSettings.ts\n"));

/***/ })

});