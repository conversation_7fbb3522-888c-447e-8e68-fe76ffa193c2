const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function applyDatabaseFixes() {
  try {
    console.log('🔧 Starting database fixes...')
    
    // Read the SQL file
    const sqlContent = fs.readFileSync(path.join(__dirname, 'fix_database_issues.sql'), 'utf8')
    
    // Split SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.trim().length === 0) {
        continue
      }
      
      try {
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
        
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        })
        
        if (error) {
          console.warn(`⚠️  Warning on statement ${i + 1}: ${error.message}`)
          // Continue with other statements even if one fails
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`)
        }
      } catch (err) {
        console.warn(`⚠️  Error on statement ${i + 1}: ${err.message}`)
        // Continue with other statements
      }
    }
    
    console.log('🎉 Database fixes completed!')
    
    // Test the fixes
    await testDatabaseFixes()
    
  } catch (error) {
    console.error('❌ Error applying database fixes:', error)
    process.exit(1)
  }
}

async function testDatabaseFixes() {
  console.log('🧪 Testing database fixes...')
  
  try {
    // Test 1: Check admin_settings table
    const { data: adminSettings, error: adminError } = await supabase
      .from('admin_settings')
      .select('key, value')
      .limit(1)
    
    if (adminError) {
      console.error('❌ Admin settings test failed:', adminError.message)
    } else {
      console.log('✅ Admin settings table is working')
    }
    
    // Test 2: Check users table structure
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, kyc_status, referral_code')
      .limit(1)
    
    if (usersError) {
      console.error('❌ Users table test failed:', usersError.message)
    } else {
      console.log('✅ Users table structure is correct')
    }
    
    // Test 3: Check KYC tables
    const { data: kycSubmissions, error: kycError } = await supabase
      .from('kyc_submissions')
      .select('id, user_id, status')
      .limit(1)
    
    if (kycError) {
      console.error('❌ KYC submissions test failed:', kycError.message)
    } else {
      console.log('✅ KYC tables are working')
    }
    
    console.log('🎯 Database tests completed!')
    
  } catch (error) {
    console.error('❌ Error testing database:', error)
  }
}

// Alternative method using direct SQL execution
async function executeDirectSQL() {
  try {
    console.log('🔧 Applying fixes using direct SQL execution...')
    
    // Read and execute the SQL file content
    const sqlContent = fs.readFileSync(path.join(__dirname, 'fix_database_issues.sql'), 'utf8')
    
    // Try to execute the entire SQL content at once
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    })
    
    if (error) {
      console.error('❌ Direct SQL execution failed:', error.message)
      // Fall back to statement-by-statement execution
      await applyDatabaseFixes()
    } else {
      console.log('✅ Direct SQL execution successful')
      await testDatabaseFixes()
    }
    
  } catch (error) {
    console.error('❌ Error with direct SQL execution:', error)
    // Fall back to statement-by-statement execution
    await applyDatabaseFixes()
  }
}

// Run the fixes
if (require.main === module) {
  executeDirectSQL()
}

module.exports = { applyDatabaseFixes, testDatabaseFixes }
